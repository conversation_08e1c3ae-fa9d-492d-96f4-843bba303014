import { Component } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Branch } from '../../models/interface/allinterfaces';
import { CommonService } from '../../services/common.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent {
  loginForm: FormGroup;
  branches: Branch[] = [];
  loading = false;
  errorMessage = '';

  constructor(private fb: FormBuilder, private cs: CommonService, private router: Router) {
    this.loginForm = this.fb.group({
      branch: ['', Validators.required],
      userId: ['', Validators.required],
      password: ['', Validators.required]
    });
  }

  ngOnInit() {
    this.cs.getBranchList().subscribe((res) => {
      this.branches = res.data;
    });
  }

  onSubmit() {
    if (this.loginForm.valid) {
      this.loading = true;
      this.errorMessage = '';
      
      const loginData = this.loginForm.value;
      this.cs.getLogin(loginData).subscribe({
        next: (res) => {
          this.loading = false;
          // console.log('Login Response:', res.token);
          if (res.status === true) {
            // Store token in localStorage
            localStorage.setItem('token', res.token ?? '');
            // Store user if it exists in the response
            if ('user' in res) {
              localStorage.setItem('user', JSON.stringify((res as any).user ?? {}));
            }
            // Navigate to dashboard
            this.router.navigate(['/dashboard']);
          } else {
            this.errorMessage = res.message || 'Login failed';
          }
        },
        error: (err) => {
          this.loading = false;
          this.errorMessage = 'An error occurred. Please try again.';
          console.error('Login error:', err);
        }
      });
    }
  }

  onForgotPassword(event: Event) {
    event.preventDefault();
    // Handle forgot password
  }
}
