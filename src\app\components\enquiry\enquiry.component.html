<div class="container mx-auto px-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">Enquiries</h1>
      
      <!-- Academic Year Dropdown -->
      <div class="relative">
        <select 
          [(ngModel)]="selectedYear" 
          (change)="onYearChange()" 
          class="appearance-none bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
        >
          <option *ngFor="let year of academicYears" [value]="year.id">{{ year.name }}</option>
        </select>
        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
          <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="flex justify-center items-center py-10">
      <svg class="animate-spin h-10 w-10 text-teal-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>

    <!-- Error State -->
    <div *ngIf="error && !loading" class="bg-red-100 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
      <p>Failed to load enquiry data. Please try again later.</p>
      <button (click)="fetchEnquiryData()" class="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
        Retry
      </button>
    </div>

    <!-- Enquiry Categories Grid -->
    <div *ngIf="!loading && !error" class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div *ngFor="let category of categories" class="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-3">
        <div class="p-6" [routerLink]="['/enquiry', category.slug]" class="cursor-pointer">
          <div class="flex justify-between items-center">
            <h2 class="text-lg font-semibold text-teal-600">
              {{ category.name }}
            </h2>
            <div class="text-right">
              <p class="text-sm text-gray-600">Enquiries</p>
              <p class="text-xl font-bold text-gray-800">{{ category.count }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add New Enquiry Button -->
    <button 
      [routerLink]="['/enquiry/create']"
      class="fixed bottom-6 right-6 w-14 h-14 rounded-full bg-teal-600 text-white flex items-center justify-center shadow-lg hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
    </button>
  </div>
