export interface ApiResponse<T> {
  status: boolean;
  data: T;
  message?: string;
  token?: string;
}

export interface Branch {
  id: number;
  name: string;
  // other properties
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface User {
  id: number;
  user: string;
  name: string;
  role: string;
  avatar?: string;
}

export interface DashboardStats {
  students: {
    capacity: number;
  };
  teachers: {
    total: number;
    capacity: number;
  };
  staff: {
    total: number;
    capacity: number;
  };
}

export interface AcademicYear {
  id: string;
  name: string;
}

export interface EnquiryCategory {
  id: number;
  name: string;
  count: number;
  slug: string;
}

export interface EnquiryData {
  academicYears: AcademicYear[];
  enquiryData: EnquiryCategory[];
}

export interface EnquiryListData {
  id: number;
  student_name: string;
  dob: string;
  father_name: string;
  guardian_name: string;
  current_grade: string;
  mobile: string;
  created_at: string;
}

export interface EnquiryList {
  enquiryListData: EnquiryListData[];
}

export interface ClassListData {
  id: number;
  name: string;
  slug: string;
}

export interface ClassList {
  classListData: ClassListData[];
}
