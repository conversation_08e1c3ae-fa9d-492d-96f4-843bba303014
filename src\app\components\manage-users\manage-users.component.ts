import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { CommonService } from '../../services/common.service';

@Component({
  selector: 'app-manage-users',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './manage-users.component.html',
  styleUrl: './manage-users.component.css'
})
export class ManageUsersComponent implements OnInit {
  loading = true;
  error = false;
  searchText: string = '';
  userListData: any[] = []; // Adjust type as needed
  categorySlug: string = ''; // Add a property to store the category slug from the route

  constructor(private commonService: CommonService) {}

  ngOnInit(): void {
    // Initialize user list data or fetch from a service if needed
    this.fetchUserListData();
  }

  fetchUserListData(): void {
    this.loading = true;
    // Fetch user list data from a service
    // For now, we'll just simulate this by setting some dummy data
    this.commonService.getUserList().subscribe({
      next: (response) => {
        console.log('User Data Response:', response);
        if (response.status) {
          // Handle successful response
          this.userListData = response.data.userListData;
        } else {
          this.error = true;
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error fetching user data:', err);
        this.error = true;
        this.loading = false;
      }
    });
  }
  deleteUser(userId: number): void {
    // Simulate user deletion
    this.userListData = this.userListData.filter(user => user.id !== userId);
  }
}
