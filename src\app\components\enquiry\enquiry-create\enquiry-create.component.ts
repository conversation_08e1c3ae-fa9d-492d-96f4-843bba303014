import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { CommonService } from '../../../services/common.service';
import flatpickr from 'flatpickr';

@Component({
  selector: 'app-enquiry-create',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule, RouterModule],
  templateUrl: './enquiry-create.component.html',
  styleUrl: './enquiry-create.component.css'
})
export class EnquiryCreateComponent implements OnInit, AfterViewInit {

  @ViewChild('dobInput') dobInput!: ElementRef;
  
  activeTab = 'student';
  enquiryForm!: FormGroup;
  loading = false;
  error = false;
  submitted = false;
  categorySlug = ''; // Added to store the category slug
  
  // For gender selection
  selectedGender: string = '';
  classListData: any = [];

  constructor(private commonService: CommonService, private fb: FormBuilder, private router: Router, private route: ActivatedRoute) {} // Added route to constructor

  ngOnInit(): void {
    this.categorySlug = this.route.snapshot.params['categorySlug']; // Get the category slug from the route
    this.classDetails();
    this.initForm();

    // Check for edit mode (e.g., /enquiry/edit/:id)
    const enquiryId = this.route.snapshot.params['id'];
    if (enquiryId) {
      this.fetchEnquiry(enquiryId);
    }
  }

  ngAfterViewInit(): void {
    flatpickr(this.dobInput.nativeElement, {
      dateFormat: 'd-m-Y',
      maxDate: new Date(new Date().setFullYear(new Date().getFullYear() - 2)),
      allowInput: true
    });
  }

  classDetails(): void {
    this.commonService.getClassList().subscribe({
      next: (response) => {
        if (response.status) {
          // Handle successful response
          this.classListData = response.data.classListData;

          // Set class_id based on categorySlug and classListData
          if (this.categorySlug && this.classListData.length) {
            const matchedClass = this.classListData.find(
              (cls: any) => cls.slug === this.categorySlug
            );
            if (matchedClass) {
              // Patch the value if form is already created
              if (this.enquiryForm && this.enquiryForm.get('student')) {
                this.enquiryForm.get('student.class_id')?.setValue(matchedClass.id);
              }
            }
          }
        } else {
          this.error = true;
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error fetching class list:', err);
        this.error = true;
        this.loading = false;
      }
    });
  }

  initForm(): void {
    this.enquiryForm = this.fb.group({
      student: this.fb.group({
        student_name: ['', Validators.required],
        student_date_of_birth: ['', Validators.required],
        student_gender: ['', Validators.required],
        student_current_grade: ['', Validators.required],
        class_id: [this.categorySlug ? this.categorySlug : '', Validators.required],
        student_previous_school: [''],
        student_aadhaar_number: ['', [Validators.required, Validators.pattern(/^\d{12}$/)]]
      }),
      parent: this.fb.group({
        father_name: ['', Validators.required],
        father_occupation: ['', Validators.required],
        father_phone_number: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
        mother_name: ['', Validators.required],
        mother_occupation: ['', Validators.required],
        mother_phone_number: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]]
      }),
      guardian: this.fb.group({
        guardian_name: [''],
        guardian_occupation: [''],
        guardian_phone_number: ['', Validators.pattern(/^\d{10}$/)]
      }),
      other: this.fb.group({
        address_1: ['', Validators.required],
        address_2: [''],
        landmark: [''],
        city: ['', Validators.required],
        state: ['', Validators.required],
        pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
      })
    });
  }

  fetchEnquiry(id: string): void {
    this.loading = true;
    const enquiry = { id: id, class: this.categorySlug }; // Use plain object
    this.commonService.getEnquiryById(enquiry).subscribe({
      next: (response) => {
        if (response.status && response.data) {
          console.log('Enquiry Data:', response.data.enquiryData);
          const enquiryData = response.data.enquiryData;
          // Patch the form with the existing enquiry data
          this.enquiryForm.patchValue({
            student: {
              student_name: enquiryData.student_name,
              student_date_of_birth: enquiryData.student_date_of_birth,
              student_gender: enquiryData.student_gender,
              student_current_grade: enquiryData.student_current_grade,
              class_id: enquiryData.class_id,
              student_previous_school: enquiryData.student_previous_school,
              student_aadhaar_number: enquiryData.student_aadhaar_number
            },
            parent: {
              father_name: enquiryData.father_name,
              father_occupation: enquiryData.father_occupation,
              father_phone_number: enquiryData.father_phone_number,
              mother_name: enquiryData.mother_name,
              mother_occupation: enquiryData.mother_occupation,
              mother_phone_number: enquiryData.mother_phone_number
            },
            guardian: {
              guardian_name: enquiryData.guardian_name,
              guardian_occupation: enquiryData.guardian_occupation,
              guardian_phone_number: enquiryData.guardian_phone_number
            },
            other: {
              address_1: enquiryData.address_1,
              address_2: enquiryData.address_2,
              landmark: enquiryData.landmark,
              city: enquiryData.city,
              state: enquiryData.state,
              pincode: enquiryData.pincode
            }
          });
          // Set gender button state if needed
          this.selectedGender = enquiryData.student_gender;
        } else {
          this.error = true;
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error fetching enquiry:', err);
        this.error = true;
        this.loading = false;
      }
    });
  }

  onTabClick(targetTab: string) {
    this.nextTab(targetTab);
  }

  // Navigation methods
  nextTab(targetTab?: string): void {
    if (this.activeTab === 'student' && this.studentForm.invalid) {
      this.studentForm.markAllAsTouched();
      return;
    }
    if (this.activeTab === 'parent' && this.parentForm.invalid) {
      this.parentForm.markAllAsTouched();
      return;
    }
    if (this.activeTab === 'guardian' && this.guardianForm.invalid) {
      this.guardianForm.markAllAsTouched();
      return;
    }
    if (this.activeTab === 'other' && this.otherForm.invalid) {
      this.otherForm.markAllAsTouched();
      return;
    }

    // If called from tab click, go to that tab
    if (targetTab) {
      this.activeTab = targetTab;
      return;
    }

    // If called from Next button, go to the next tab in order
    if (this.activeTab === 'student') this.activeTab = 'parent';
    else if (this.activeTab === 'parent') this.activeTab = 'guardian';
    else if (this.activeTab === 'guardian') this.activeTab = 'other';
  }

  prevTab(): void {
    if (this.activeTab === 'parent') {
      this.activeTab = 'student';
    } else if (this.activeTab === 'guardian') {
      this.activeTab = 'parent';
    } else if (this.activeTab === 'other') {
      this.activeTab = 'guardian';
    }
  }

  // Getters for form controls
  get studentForm() { return this.enquiryForm.get('student') as FormGroup; }
  get parentForm() { return this.enquiryForm.get('parent') as FormGroup; }
  get guardianForm() { return this.enquiryForm.get('guardian') as FormGroup; }
  get otherForm() { return this.enquiryForm.get('other') as FormGroup; }

  // Gender selection
  selectGender(gender: string): void {
    this.selectedGender = gender;
    this.studentForm.get('student_gender')?.setValue(gender);
  }

  // Form submission
  submitEnquiry(): void {
    this.submitted = true;
    
    if (this.enquiryForm.invalid) {
      // Find the first tab with errors and navigate to it
      if (this.studentForm.invalid) {
        this.activeTab = 'student';
      } else if (this.parentForm.invalid) {
        this.activeTab = 'parent';
      } else if (this.guardianForm.invalid) {
        this.activeTab = 'guardian';
      }
      return;
    }
    
    this.loading = true;
    // Merge all fields into a single object
    const mergedData = {
      ...this.enquiryForm.value.student,
      ...this.enquiryForm.value.parent,
      ...this.enquiryForm.value.guardian,
      ...this.enquiryForm.value.other,
      branch_id: 1
    };

    this.commonService.storeEnquiry(mergedData).subscribe({
      next: (response) => {
        if (response.status) {
          alert(response.message);
          this.submitted = false;
          this.enquiryForm.reset();
          this.activeTab = 'student';
          this.selectedGender = '';
          this.router.navigate(['/enquiry/', this.categorySlug]);
        } else {
          this.error = true;
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error submitting enquiry:', err);
        this.error = true;
        this.loading = false;
      }
    });
  }
}
