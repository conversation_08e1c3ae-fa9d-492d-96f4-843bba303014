<div class="container mx-auto px-4 py-6">

    <form [formGroup]="userForm" (ngSubmit)="submitUser()" class="bg-white p-6 rounded-lg shadow-md">
        <!-- Tabs -->
        <div class="mb-6 border-b border-gray-200">
            <nav class="flex space-x-4" aria-label="Tabs">
                <button type="button" class="py-2 px-4 text-sm font-medium"
                    [ngClass]="{ 'border-b-2 border-teal-600 text-teal-600': activeTab === 'basic', 'text-gray-500': activeTab !== 'basic' }" (click)="onTabClick('basic')">
                    Basic Details
                </button>
                <button type="button" class="py-2 px-4 text-sm font-medium"
                    [ngClass]="{ 'border-b-2 border-teal-600 text-teal-600': activeTab === 'address', 'text-gray-500': activeTab !== 'address' }" (click)="onTabClick('address')">
                    Address Details
                </button>
                <button type="button" class="py-2 px-4 text-sm font-medium"
                    [ngClass]="{ 'border-b-2 border-teal-600 text-teal-600': activeTab === 'subjects', 'text-gray-500': activeTab !== 'subjects' }" (click)="onTabClick('subjects')">
                    Subjects Details
                </button>
            </nav>
        </div>

        <!-- Basic Details Tab -->
        <div *ngIf="activeTab === 'basic'" formGroupName="basic">
        
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">

                <div class="md:col-span-2">
                    <label for="profilePicture" class="block text-sm font-medium text-gray-700 mb-1">Profile Picture</label>
                    <div class="flex items-start space-x-4">
                        <!-- File Input Section -->
                        <div class="flex-1">
                            <input
                                type="file"
                                id="profilePicture"
                                accept="image/*"
                                (change)="onImageSelected($event)"
                                class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                                [ngClass]="{'border-red-500': (basicForm.get('profile_picture')?.invalid && (basicForm.get('profile_picture')?.touched || submitted)) || imageError}">

                            <!-- Error Messages -->
                            <div *ngIf="imageError" class="mt-1 text-xs text-red-600">
                                {{ imageError }}
                            </div>
                            <div *ngIf="basicForm.get('profile_picture')?.invalid && (basicForm.get('profile_picture')?.touched || submitted) && !imageError"
                                 class="mt-1 text-xs text-red-600">
                                Profile picture is required
                            </div>

                            <!-- File Info -->
                            <div *ngIf="selectedImageFile && !imageError" class="mt-1 text-xs text-gray-600">
                                Selected: {{ selectedImageFile.name }} ({{ (selectedImageFile.size / 1024 / 1024).toFixed(2) }} MB)
                            </div>
                        </div>

                        <!-- Image Preview Section -->
                        <div *ngIf="imagePreviewUrl" class="flex-shrink-0 image-preview-container">
                            <div class="relative">
                                <img
                                    [src]="imagePreviewUrl"
                                    alt="Profile Preview"
                                    class="w-20 h-20 object-cover rounded-lg border border-gray-300 shadow-sm">
                                <button
                                    type="button"
                                    (click)="clearImage()"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                                    title="Remove image">
                                    ×
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 mt-1 text-center">Click × to remove</p>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                    <select id="role" formControlName="role"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
                        <option value="">Select Role</option>
                        <option value="admin">Admin</option>
                        <option value="editor">Editor</option>
                        <option value="viewer">Viewer</option>
                    </select>
                    <p *ngIf="basicForm.get('role')?.invalid && (basicForm.get('role')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">Role is required</p>
                </div>

                <div>
                    <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                    <input type="text" id="fullName" formControlName="name"
                    placeholder="Enter Full Name"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': basicForm.get('name')?.invalid && (basicForm.get('name')?.touched || submitted)}">
                    <p *ngIf="basicForm.get('name')?.invalid && (basicForm.get('name')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">Full name is required</p>
                </div>

                <div>
                    <label for="currentGrade" class="block text-sm font-medium text-gray-700 mb-1">Father / Spouse Name</label>
                    <input type="text" id="currentGrade" formControlName="father_or_spouse_name"
                    placeholder="Enter Father / Spouse Name"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': basicForm.get('father_or_spouse_name')?.invalid && (basicForm.get('father_or_spouse_name')?.touched || submitted)}">
                    <p *ngIf="basicForm.get('father_or_spouse_name')?.invalid && (basicForm.get('father_or_spouse_name')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">Father / Spouse name is required</p>
                </div>
                
                <div>
                    <label for="dob" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                    <div class="relative">
                    <input type="text"
                        #dobInput
                        readonly
                        autocomplete="off"
                        class="datepicker w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500 pr-10 bg-white"
                        formControlName="date_of_birth"
                        placeholder="Select Date of Birth"
                        [ngClass]="{'border-red-500': basicForm.get('date_of_birth')?.invalid && (basicForm.get('date_of_birth')?.touched || submitted)}"
                    />
                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    </div>
                    <p *ngIf="basicForm.get('date_of_birth')?.invalid && (basicForm.get('date_of_birth')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">Date of birth is required</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                    <div class="flex space-x-4">
                    <button type="button" 
                        class="px-4 py-2 rounded-md text-sm font-medium focus:outline-none"
                        [ngClass]="{'bg-teal-600 text-white': selectedGender === 'male', 'bg-gray-200 text-gray-700': selectedGender !== 'male'}"
                        (click)="selectGender('male')">
                        Male
                    </button>
                    <button type="button" 
                        class="px-4 py-2 rounded-md text-sm font-medium focus:outline-none"
                        [ngClass]="{'bg-teal-600 text-white': selectedGender === 'female', 'bg-gray-200 text-gray-700': selectedGender !== 'female'}"
                        (click)="selectGender('female')">
                        Female
                    </button>
                    </div>
                    <p *ngIf="basicForm.get('gender')?.invalid && (basicForm.get('gender')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">Gender is required</p>
                </div>           
                
                <div>
                    <label for="interestedGrade" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                    <input type="text" id="interestedGrade" formControlName="phone" placeholder="Enter Phone Number"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': basicForm.get('phone')?.invalid && (basicForm.get('phone')?.touched || submitted)}">
                    <p *ngIf="basicForm.get('phone')?.invalid && (basicForm.get('phone')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">Phone number is required</p>
                </div>
                
                <div>
                    <label for="previousSchool" class="block text-sm font-medium text-gray-700 mb-1">Email ID</label>
                    <input type="text" id="previousSchool" formControlName="email"
                    placeholder="Enter Email ID"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': basicForm.get('email')?.invalid && (basicForm.get('email')?.touched || submitted)}">
                    <p *ngIf="basicForm.get('email')?.invalid && (basicForm.get('email')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">Email ID is required</p>
                </div>

                <div>
                    <label for="aadhaar" class="block text-sm font-medium text-gray-700 mb-1">Aadhaar Number</label>
                    <input type="text" id="aadhaar" formControlName="aadhaar_number"
                    placeholder="Enter Aadhaar Number"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': basicForm.get('aadhaar_number')?.invalid && (basicForm.get('aadhaar_number')?.touched || submitted)}">
                    <p *ngIf="basicForm.get('aadhaar_number')?.invalid && (basicForm.get('aadhaar_number')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">
                    <span *ngIf="basicForm.get('aadhaar_number')?.errors?.['required']">Aadhaar number is required</span>
                    <span *ngIf="basicForm.get('aadhaar_number')?.errors?.['pattern']">Aadhaar must be 12 digits</span>
                    </p>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <input type="password" id="password" formControlName="password"
                    placeholder="Enter Password"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': basicForm.get('password')?.invalid && (basicForm.get('password')?.touched || submitted)}">
                    <p *ngIf="basicForm.get('password')?.invalid && (basicForm.get('password')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">Password is required</p>
                </div>

                <div>
                    <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                    <input type="password" id="confirmPassword" formControlName="confirm_password"
                    placeholder="Confirm Password"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': basicForm.get('confirm_password')?.invalid && (basicForm.get('confirm_password')?.touched || submitted)}">
                    <p *ngIf="basicForm.get('confirm_password')?.invalid && (basicForm.get('confirm_password')?.touched || submitted)" 
                    class="mt-1 text-xs text-red-600">Confirm password is required</p>
                </div>
            </div>
            
            <div class="md:col-span-2 flex justify-end mt-6">
            <button type="button" (click)="nextTab('address')"
                class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg"
                [disabled]="basicForm.invalid || !selectedImageFile">
                Next
            </button>
            </div>
        </div>

        <!-- Address Details Tab -->
        <div *ngIf="activeTab === 'address'" formGroupName="address">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div class="md:col-span-2">
                    <label for="address1" class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                    <input type="text" id="address1" formControlName="address_1"
                    placeholder="Enter Address Line 1"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': addressForm.get('address_1')?.invalid && (addressForm.get('address_1')?.touched || submitted)}">
                    <p *ngIf="addressForm.get('address_1')?.invalid && (addressForm.get('address_1')?.touched || submitted)"
                    class="mt-1 text-xs text-red-600">Address Line 1 is required</p>
                </div>

                <div class="md:col-span-2">
                    <label for="address2" class="block text-sm font-medium text-gray-700 mb-1">Address Line 2 (Optional)</label>
                    <input type="text" id="address2" formControlName="address_2"
                    placeholder="Enter Address Line 2"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500">
                </div>

                <div>
                    <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                    <input type="text" id="city" formControlName="city"
                    placeholder="Enter City"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': addressForm.get('city')?.invalid && (addressForm.get('city')?.touched || submitted)}">
                    <p *ngIf="addressForm.get('city')?.invalid && (addressForm.get('city')?.touched || submitted)"
                    class="mt-1 text-xs text-red-600">City is required</p>
                </div>

                <div>
                    <label for="state" class="block text-sm font-medium text-gray-700 mb-1">State</label>
                    <input type="text" id="state" formControlName="state"
                    placeholder="Enter State"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': addressForm.get('state')?.invalid && (addressForm.get('state')?.touched || submitted)}">
                    <p *ngIf="addressForm.get('state')?.invalid && (addressForm.get('state')?.touched || submitted)"
                    class="mt-1 text-xs text-red-600">State is required</p>
                </div>

                <div>
                    <label for="zip" class="block text-sm font-medium text-gray-700 mb-1">ZIP Code</label>
                    <input type="text" id="zip" formControlName="zip"
                    placeholder="Enter ZIP Code"
                    class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-teal-500"
                    [ngClass]="{'border-red-500': addressForm.get('zip')?.invalid && (addressForm.get('zip')?.touched || submitted)}">
                    <p *ngIf="addressForm.get('zip')?.invalid && (addressForm.get('zip')?.touched || submitted)"
                    class="mt-1 text-xs text-red-600">
                    <span *ngIf="addressForm.get('zip')?.errors?.['required']">ZIP code is required</span>
                    <span *ngIf="addressForm.get('zip')?.errors?.['pattern']">ZIP code must be 6 digits</span>
                    </p>
                </div>
            </div>

            <div class="md:col-span-2 flex justify-between mt-6">
                <button type="button" (click)="prevTab()" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                    Previous
                </button>
                <button type="button" (click)="nextTab('subjects')"
                    class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg"
                    [disabled]="addressForm.invalid">
                    Next
                </button>
            </div>
        </div>

        <!-- Subjects Details Tab -->
        <div *ngIf="activeTab === 'subjects'" formGroupName="subjects">
            <div class="grid grid-cols-1 gap-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Subjects (Optional)</label>
                    <p class="text-sm text-gray-600 mb-3">Select subjects that this user will be associated with.</p>
                    <div class="space-y-2">
                        <!-- This would typically be populated from a service -->
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2" value="math">
                            <span>Mathematics</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2" value="science">
                            <span>Science</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2" value="english">
                            <span>English</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2" value="history">
                            <span>History</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="md:col-span-2 flex justify-between mt-6">
                <button type="button" (click)="prevTab()" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                    Previous
                </button>
                <button type="submit"
                    class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg"
                    [disabled]="loading">
                    <span *ngIf="loading">Creating...</span>
                    <span *ngIf="!loading">Create User</span>
                </button>
            </div>
        </div>
    </form>
</div>