import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonService } from '../../../services/common.service';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import flatpickr from 'flatpickr';

@Component({
  selector: 'app-user-create',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule],
  templateUrl: './user-create.component.html',
  styleUrl: './user-create.component.css'
})
export class UserCreateComponent implements OnInit, AfterViewInit {

  @ViewChild('dobInput') dobInput!: ElementRef;

  activeTab = 'basic';
  userForm!: FormGroup;
  loading = false;
  error = false;
  submitted = false;
  // For gender selection
  selectedGender: string = '';

  // Image handling properties
  selectedImageFile: File | null = null;
  imagePreviewUrl: string | null = null;
  imageError: string | null = null;

  // Allowed image types
  private allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  private maxImageSize = 5 * 1024 * 1024; // 5MB

  // Getters for form controls
  get basicForm() { return this.userForm.get('basic') as FormGroup; }
  get addressForm() { return this.userForm.get('address') as FormGroup; }
  get subjectsForm() { return this.userForm.get('subjects') as FormGroup; }

  constructor(private commonService: CommonService, private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initForm();
  }

  ngAfterViewInit(): void {
    // Initialize datepicker
    flatpickr(this.dobInput.nativeElement, {
      dateFormat: 'd-m-Y',
      maxDate: new Date(new Date().setFullYear(new Date().getFullYear() - 18)),
      allowInput: true
    });
  }

  initForm(): void {
    this.userForm = this.fb.group({
      basic: this.fb.group({
        profile_picture: [''], // Remove required validator as we handle file validation separately
        name: ['', Validators.required],
        father_or_spouse_name: ['', Validators.required],
        date_of_birth: ['', Validators.required],
        gender: ['', Validators.required],
        phone: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
        email: ['', [Validators.required, Validators.email]],
        password: ['', Validators.required],
        confirm_password: ['', Validators.required],
        role: ['', Validators.required],
        aadhaar_number: ['', [Validators.required, Validators.pattern(/^\d{12}$/)]]
      }),
      address: this.fb.group({
        address_1: ['', Validators.required],
        address_2: [''],
        city: ['', Validators.required],
        state: ['', Validators.required],
        zip: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
      }),
      subjects: this.fb.group({
        subject_ids: [[]]
      })
    });
  }

  // Gender selection
  selectGender(gender: string): void {
    this.selectedGender = gender;
    this.basicForm.get('gender')?.setValue(gender);
  }

  // Image handling methods
  onImageSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    // Reset previous state
    this.imageError = null;
    this.imagePreviewUrl = null;
    this.selectedImageFile = null;

    if (!file) {
      this.basicForm.get('profile_picture')?.setValue('');
      return;
    }

    // Validate file type
    if (!this.allowedImageTypes.includes(file.type)) {
      this.imageError = 'Please select a valid image file (JPEG, PNG, or GIF)';
      input.value = '';
      this.basicForm.get('profile_picture')?.setValue('');
      return;
    }

    // Validate file size
    if (file.size > this.maxImageSize) {
      this.imageError = 'Image size must be less than 5MB';
      input.value = '';
      this.basicForm.get('profile_picture')?.setValue('');
      return;
    }

    // Store the file and create preview
    this.selectedImageFile = file;
    this.basicForm.get('profile_picture')?.setValue(file.name);

    // Create image preview
    const reader = new FileReader();
    reader.onload = (e) => {
      this.imagePreviewUrl = e.target?.result as string;
    };
    reader.onerror = () => {
      this.imageError = 'Error reading the image file';
      this.clearImage();
    };
    reader.readAsDataURL(file);
  }

  // Clear image selection
  clearImage(): void {
    this.selectedImageFile = null;
    this.imagePreviewUrl = null;
    this.imageError = null;
    this.basicForm.get('profile_picture')?.setValue('');

    // Clear the file input
    const fileInput = document.getElementById('profilePicture') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  onTabClick(targetTab: string) {
    this.nextTab(targetTab);
  }

  // Navigation methods
  nextTab(targetTab?: string): void {
    if (this.activeTab === 'basic' && (this.basicForm.invalid || !this.selectedImageFile)) {
      this.basicForm.markAllAsTouched();
      if (!this.selectedImageFile) {
        this.imageError = 'Profile picture is required';
      }
      return;
    }
    if (this.activeTab === 'address' && this.addressForm.invalid) {
      this.addressForm.markAllAsTouched();
      return;
    }
    if (this.activeTab === 'subjects' && this.subjectsForm.invalid) {
      this.subjectsForm.markAllAsTouched();
      return;
    }
    // If called from tab click, go to that tab
    if (targetTab) {
      this.activeTab = targetTab;
      return;
    }

    // If called from Next button, go to the next tab in order
    if (this.activeTab === 'basic') this.activeTab = 'address';
    else if (this.activeTab === 'address') this.activeTab = 'subjects';
  }

  prevTab(): void {
    if (this.activeTab === 'address') {
      this.activeTab = 'basic';
    } else if (this.activeTab === 'subjects') {
      this.activeTab = 'address';
    }
  }

  submitUser(): void {
    this.submitted = true;

    // Check for image file requirement
    if (!this.selectedImageFile) {
      this.imageError = 'Profile picture is required';
      this.activeTab = 'basic';
      return;
    }

    if (this.userForm.invalid) {
      // Find the first tab with errors and navigate to it
      if (this.basicForm.invalid) {
        this.activeTab = 'basic';
      } else if (this.addressForm.invalid) {
        this.activeTab = 'address';
      } else if (this.subjectsForm.invalid) {
        this.activeTab = 'subjects';
      }
      return;
    }

    this.loading = true;

    // Create FormData for file upload
    const formData = new FormData();

    // Add the image file if selected
    if (this.selectedImageFile) {
      formData.append('profile_picture', this.selectedImageFile);
    }

    // Add all other form fields
    const userFormValue = this.userForm.value;

    // Flatten the nested form structure and append to FormData
    Object.keys(userFormValue.basic).forEach(key => {
      if (key !== 'profile_picture') { // Skip profile_picture as it's handled separately
        formData.append(key, userFormValue.basic[key]);
      }
    });

    Object.keys(userFormValue.address).forEach(key => {
      formData.append(key, userFormValue.address[key]);
    });

    Object.keys(userFormValue.subjects).forEach(key => {
      if (Array.isArray(userFormValue.subjects[key])) {
        formData.append(key, JSON.stringify(userFormValue.subjects[key]));
      } else {
        formData.append(key, userFormValue.subjects[key]);
      }
    });

    // Call the service to create a user
    this.commonService.createUser(formData).subscribe({
      next: (response) => {
        if (response.status) {
          // Handle successful response
          console.log('User created successfully:', response.data);
          alert('User created successfully!');
          this.resetForm();
        } else {
          this.error = true;
          alert('Failed to create user. Please try again.');
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error creating user:', err);
        this.error = true;
        alert('An error occurred while creating the user. Please try again.');
        this.loading = false;
      }
    });
  }

  // Reset form method
  private resetForm(): void {
    this.userForm.reset();
    this.submitted = false;
    this.selectedGender = '';
    this.clearImage();
    this.activeTab = 'basic';
  }
}
