<div class="container mx-auto px-4">
    <div class="flex justify-between items-center mb-6">
        <input 
            type="text" 
            [(ngModel)]="searchText" 
            placeholder="Search users..." 
            class="border border-gray-300 rounded-md py-2 px-3 mr-4 focus:outline-none focus:ring-2 focus:ring-teal-500"
        />
        <a [routerLink]="['/user/create']" class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg flex items-center">
            <span>Add</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
        </a>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="flex justify-center items-center py-10">
        <svg class="animate-spin h-10 w-10 text-teal-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
    </div>

    
    <!-- ...existing code... -->
    <div *ngIf="!loading && !error" class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200 rounded-lg shadow">
            <thead class="bg-gray-100">
                <tr>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Sl No.</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Name</th>                    
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Email</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Role</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Mobile</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Created At</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let user of userListData; let i = index" class="border-t">
                    <td class="px-4 py-2">{{ i + 1 }}</td>
                    <td class="px-4 py-2">{{ user.name }}</td>
                    <td class="px-4 py-2">{{ user.email }}</td>
                    <td class="px-4 py-2">{{ user.role }}</td>
                    <td class="px-4 py-2">{{ user.mobile }}</td>
                    <td class="px-4 py-2">{{ user.created_at | date:'dd MMM yyyy' }}</td>
                    <td class="px-4 py-2">
                        <a [routerLink]="['/user/edit', user.id]" class="text-teal-600 hover:text-teal-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M17.414 2.586a2 2 0 00-2.828 0l-11 11a2 2 0 00-.586 1.414V18a2 2 0 002 2h3.586a2 2 0 001.414-.586l11-11a2 2 0 000-2.828l-3.172-3.172zM15.414 4L16 4.586l-1.414 1.414L14 5.414l1.414-1.414zM6.586 16H5v-1.586l9.172-9.172L15.414 6l-9.172 9.172z" />
                            </svg>
                        </a>
                        <button (click)="deleteUser(user.id)" class="text-red-600 hover:text-red-800">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M6 6v10a1 1 0 001 1h6a1 1 0 001-1V6H6zm8-4H6a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2zM8.5 4h3a1.5 1.5 0 110-3h-3A1.5 1.5 0 018.5 4z" />
                            </svg>
                        </button>
                    </td> <!-- Add your actions here -->
                </tr>
            </tbody>
        </table>
    </div>

</div>