import { Component, OnInit } from '@angular/core';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SharedModule } from './shared/shared.module';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, CommonModule, SharedModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  title = 'happy-feet-ui';
  shouldShowLayout = false;

  // Routes that should NOT show the main layout
  private noLayoutRoutes = ['/login'];

  constructor(private router: Router) {}

  ngOnInit() {
    // Listen to route changes to determine if layout should be shown
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.shouldShowLayout = !this.noLayoutRoutes.includes(event.url);
      });

    // Set initial state based on current route
    this.shouldShowLayout = !this.noLayoutRoutes.includes(this.router.url);
  }
}
