import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonService } from '../../../services/common.service';
import { User } from '../../../models/interface/allinterfaces';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css']
})
export class SidebarComponent implements OnInit {
  user: User | null = JSON.parse(localStorage.getItem('user') || '{}') as User;
  
  constructor(private cs: CommonService, private router: Router) {}
  
  ngOnInit(): void {  }
  
  logout(): void {
    this.cs.logout().subscribe({
      next: () => {
        alert('You have been successfully logged out.');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        this.router.navigate(['/login']);
      },
      error: (err) => console.error('Error logging out:', err)
    });
  }
}
