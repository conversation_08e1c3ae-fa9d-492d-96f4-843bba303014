/* Image preview styles */
.image-preview-container {
  transition: all 0.3s ease;
}

.image-preview-container img {
  transition: transform 0.2s ease;
}

.image-preview-container img:hover {
  transform: scale(1.05);
}

/* File input styling */
input[type="file"] {
  cursor: pointer;
}

input[type="file"]::-webkit-file-upload-button {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  margin-right: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

input[type="file"]::-webkit-file-upload-button:hover {
  background: #e5e7eb;
}

/* Loading state */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Error state styling */
.error-border {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 1px #ef4444;
}

/* Success state styling */
.success-border {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 1px #10b981;
}