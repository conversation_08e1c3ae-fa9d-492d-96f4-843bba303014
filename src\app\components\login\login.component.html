<!-- Tailwind Login Screen Example -->
<div class="min-h-screen flex items-center justify-center bg-[#eaf3f5] px-4">
  <div class="w-full max-w-md bg-white rounded-none shadow-none p-8 flex flex-col">
    <h1 class="text-2xl md:text-3xl text-center font-bold mb-8 text-black leading-tight">
      Welcome back!<br>Glad to see you, Again!
    </h1>
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <select class="w-full mb-4 p-3 rounded-lg bg-gray-100 text-gray-700 focus:outline-none" formControlName="branch">
        <option value="">Select Branch</option>
        @for (item of branches; track item.id) {
          <option [value]="item.id">{{ item.name }}</option>
        }
      </select>
      <input type="text" placeholder="Enter user ID / Phone Number" formControlName="userId"
        class="w-full mb-4 p-3 rounded-lg bg-gray-100 text-gray-700 focus:outline-none" />
      <input type="password" placeholder="Enter password" formControlName="password"
        class="w-full mb-4 p-3 rounded-lg bg-gray-100 text-gray-700 focus:outline-none" />
      <div class="text-center mb-4">
        <a href="#" class="text-teal-800 font-medium hover:underline" (click)="onForgotPassword($event)">Forgot Password ?</a>
      </div>
      <button type="submit"
        class="w-full bg-teal-800 text-white py-3 rounded-lg font-medium text-lg flex items-center justify-center gap-2 hover:bg-teal-900 transition">
        Login <span class="text-xl">&rsaquo;</span>
      </button>
    </form>
  </div>
</div>